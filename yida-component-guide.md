# 宜搭组件使用指南

## 🎯 概述
本指南提供宜搭平台所有组件的详细使用说明、属性配置和最佳实践，帮助AI助手正确使用和配置组件。

## 📚 组件分类

### 1. [布局组件](#布局组件) - Layout Components
- Container 容器
- RegionalContainer 区域容器  
- TabsLayout 标签页布局

### 2. [基础组件](#基础组件) - Basic Components
- Button 按钮
- Text 文本
- Image 图片
- Link 链接
- Icon 图标
- Video 视频

### 3. [表单组件](#表单组件) - Form Components
- TextField 输入框
- TextareaField 多行输入框
- NumberField 数字输入框
- SelectField 选择器
- DateField 日期选择器
- CheckboxField 复选框
- RadioField 单选框

### 4. [高级组件](#高级组件) - Advanced Components
- Table 表格
- Tree 树形控件
- Pagination 分页器
- Progress 进度条
- Steps 步骤条

---

## 布局组件

### Container 容器
**用途**: 基础布局容器，用于组织和排列其他组件

**核心属性**:
```javascript
{
  visible: true,           // 是否可见
  style: {},              // 自定义样式
  className: '',          // CSS类名
  children: []            // 子组件
}
```

**使用场景**:
- 页面整体布局
- 组件分组
- 响应式布局

**最佳实践**:
```javascript
// 动态显示/隐藏容器
export function toggleContainer() {
  const isVisible = this.$('container_main').get('visible');
  this.$('container_main').set('visible', !isVisible);
}

// 设置容器样式
export function setContainerStyle() {
  this.$('container_main').set('style', {
    backgroundColor: '#f5f5f5',
    padding: '20px',
    borderRadius: '8px'
  });
}
```

### TabsLayout 标签页布局
**用途**: 多标签页内容展示

**核心属性**:
```javascript
{
  activeKey: 'tab1',       // 当前激活的标签
  tabs: [                  // 标签配置
    { key: 'tab1', title: '标签1' },
    { key: 'tab2', title: '标签2' }
  ],
  type: 'line',           // 标签类型: 'line' | 'card'
  size: 'medium'          // 尺寸: 'small' | 'medium' | 'large'
}
```

**事件处理**:
```javascript
// 标签切换事件
export function onTabChange() {
  const activeKey = this.params.activeKey;
  this.setState({ currentTab: activeKey });
  
  // 根据标签加载不同数据
  if (activeKey === 'tab1') {
    this.loadTab1Data();
  } else if (activeKey === 'tab2') {
    this.loadTab2Data();
  }
}
```

---

## 基础组件

### Button 按钮
**用途**: 触发操作和事件

**核心属性**:
```javascript
{
  content: '按钮',         // 按钮文案
  type: 'primary',        // 按钮类型
  size: 'medium',         // 尺寸大小
  behavior: 'NORMAL',     // 显示状态
  loading: false,         // 加载状态
  disabled: false         // 禁用状态
}
```

**按钮类型**:
- `primary` - 主要按钮（蓝色）
- `normal` - 普通按钮（白色）
- `secondary` - 次要按钮（灰色）
- `warningPrimary` - 警告按钮（红色）
- `textPrimary` - 文本按钮

**使用示例**:
```javascript
// 按钮点击事件
export function onButtonClick() {
  // 设置加载状态
  this.$('button_submit').set('loading', true);
  
  // 执行异步操作
  this.dataSourceMap.submitData.load()
    .then(() => {
      this.utils.toast({
        type: 'success',
        title: '提交成功'
      });
    })
    .finally(() => {
      // 取消加载状态
      this.$('button_submit').set('loading', false);
    });
}

// 动态设置按钮状态
export function updateButtonState() {
  const hasData = this.state.dataList.length > 0;
  this.$('button_export').set('disabled', !hasData);
}
```

### Text 文本
**用途**: 显示文本内容

**核心属性**:
```javascript
{
  content: '文本内容',     // 文本内容
  color: '#333333',       // 文字颜色
  fontSize: '14px',       // 字体大小
  fontWeight: 'normal',   // 字体粗细
  textAlign: 'left'       // 对齐方式
}
```

**数据绑定示例**:
```javascript
// 绑定动态内容
// 在属性配置中使用: state.userName
// 或使用表达式: `欢迎您，${state.userName}！`

// 通过代码设置内容
export function updateText() {
  const userName = this.utils.getLoginUserName();
  this.$('text_welcome').set('content', `欢迎您，${userName}！`);
}
```

---

## 表单组件

### TextField 输入框
**用途**: 单行文本输入

**核心属性**:
```javascript
{
  value: '',              // 输入值
  placeholder: '请输入',   // 占位提示
  htmlType: 'input',      // 输入类型
  validationType: 'text', // 验证类型
  maxLength: 200,         // 最大长度
  hasClear: true,         // 显示清除按钮
  disabled: false,        // 禁用状态
  required: false         // 必填状态
}
```

**输入类型**:
- `input` - 单行输入
- `textarea` - 多行输入
- `password` - 密码输入

**验证类型**:
- `text` - 普通文本
- `mobile` - 手机号
- `email` - 邮箱
- `url` - 网址
- `chineseID` - 身份证号

**使用示例**:
```javascript
// 获取输入值
export function getInputValue() {
  const value = this.$('textField_name').getValue();
  console.log('输入值:', value);
}

// 设置输入值
export function setInputValue() {
  this.$('textField_name').setValue('张三');
}

// 输入验证
export function validateInput() {
  this.$('textField_email').validate((errors, values) => {
    if (errors && errors.length > 0) {
      this.utils.toast({
        type: 'error',
        title: '邮箱格式不正确'
      });
    }
  });
}

// 监听输入变化
export function onInputChange() {
  const value = this.params.value;
  this.setState({ inputValue: value });
  
  // 实时验证
  if (value.length > 0 && value.length < 3) {
    this.$('textField_name').set('state', 'error');
  } else {
    this.$('textField_name').set('state', '');
  }
}
```

### SelectField 选择器
**用途**: 下拉选择

**核心属性**:
```javascript
{
  value: '',              // 选中值
  placeholder: '请选择',   // 占位提示
  dataSource: [],         // 数据源
  hasSelectAll: false,    // 显示全选
  multiple: false,        // 多选模式
  searchable: false,      // 可搜索
  disabled: false         // 禁用状态
}
```

**数据源格式**:
```javascript
// 静态数据源
const options = [
  { label: '选项1', value: 'option1' },
  { label: '选项2', value: 'option2' },
  { label: '选项3', value: 'option3' }
];

// 动态数据源
export function loadSelectOptions() {
  this.dataSourceMap.getOptions.load()
    .then(response => {
      const options = response.data.map(item => ({
        label: item.name,
        value: item.id
      }));
      this.$('selectField_category').set('dataSource', options);
    });
}
```

### DateField 日期选择器
**用途**: 日期时间选择

**核心属性**:
```javascript
{
  value: null,            // 日期值
  placeholder: '请选择日期', // 占位提示
  format: 'YYYY-MM-DD',   // 显示格式
  showTime: false,        // 显示时间
  disabledDate: null,     // 禁用日期函数
  ranges: {}              // 快捷选择
}
```

**使用示例**:
```javascript
// 设置日期值
export function setDate() {
  this.$('dateField_start').setValue(new Date());
}

// 获取日期值
export function getDate() {
  const date = this.$('dateField_start').getValue();
  console.log('选择的日期:', date);
}

// 设置日期范围限制
export function setDateRange() {
  const today = new Date();
  const maxDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后
  
  this.$('dateField_deadline').set('disabledDate', (date) => {
    return date < today || date > maxDate;
  });
}
```

---

## 高级组件

### Table 表格
**用途**: 数据展示和操作

**核心属性**:
```javascript
{
  dataSource: [],         // 数据源
  columns: [],           // 列配置
  pagination: false,     // 分页配置
  loading: false,        // 加载状态
  rowSelection: null,    // 行选择
  scroll: { x: true }    // 滚动配置
}
```

**列配置示例**:
```javascript
const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 120
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    render: (text, record) => {
      return `<button onclick="editRecord('${record.id}')">编辑</button>`;
    }
  }
];
```

**使用示例**:
```javascript
// 加载表格数据
export function loadTableData() {
  this.$('table_users').set('loading', true);
  
  this.dataSourceMap.getUserList.load({
    page: this.state.currentPage,
    pageSize: 10
  }).then(response => {
    this.$('table_users').set('dataSource', response.data);
    this.$('table_users').set('loading', false);
  });
}

// 表格行选择
export function onTableRowSelect() {
  const selectedRows = this.params.selectedRows;
  this.setState({ selectedUsers: selectedRows });
}
```

---

## 🔧 组件通用规则

### 1. 组件标识符规则
- **格式**: `组件类型_随机字符串`
- **示例**: `textField_abc123`, `button_xyz789`
- **获取**: 通过属性面板查看
- **注意**: 表单组件标识符修改会影响数据存储

### 2. 属性操作规则
```javascript
// ✅ 正确的属性操作
const value = this.$('textField_abc').get('value');
this.$('textField_abc').set('disabled', true);

// ❌ 禁止的属性操作
const value = this.$('textField_abc').value;
this.$('textField_abc').disabled = true;
```

### 3. 事件绑定规则
```javascript
// 标准事件处理函数
export function onClick() {
  // 获取事件参数
  const params = this.params;
  
  // 处理业务逻辑
  console.log('按钮被点击', params);
}

// 表单值变化事件
export function onChange() {
  const { value, name } = this.params;
  this.setState({ [name]: value });
}
```

### 4. 数据绑定规则
```javascript
// 在属性配置中使用变量绑定
state.variableName           // 绑定全局变量
state.user.name             // 绑定对象属性
state.list[0].title         // 绑定数组元素

// 表达式绑定
state.count > 0 ? '有数据' : '无数据'
`当前用户: ${state.userName}`
```

### 5. 样式设置规则
```javascript
// 设置组件样式
this.$('container_main').set('style', {
  backgroundColor: '#f5f5f5',
  padding: '20px',
  margin: '10px',
  borderRadius: '8px',
  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
});

// 设置CSS类名
this.$('text_title').set('className', 'custom-title');
```

---

## 📝 最佳实践

### 1. 组件选择原则
- **表单场景**: 优先使用表单组件
- **展示场景**: 使用基础组件和高级组件
- **布局场景**: 使用布局组件

### 2. 性能优化
- **避免频繁更新**: 批量设置属性
- **合理使用事件**: 避免不必要的事件绑定
- **数据懒加载**: 大数据量时使用分页

### 3. 用户体验
- **及时反馈**: 操作后提供明确反馈
- **状态管理**: 合理使用loading、disabled等状态
- **错误处理**: 提供友好的错误提示

### 4. 代码组织
- **函数命名**: 使用有意义的函数名
- **注释说明**: 为复杂逻辑添加注释
- **错误处理**: 完善的异常处理机制

---

**📝 说明**: 本指南涵盖了宜搭平台的核心组件使用方法，AI助手在使用组件时应严格按照规范操作，确保组件功能的正确实现和良好的用户体验。
