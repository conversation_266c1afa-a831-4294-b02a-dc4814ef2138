---
title: Pagination 翻页器
order: 9
---

# Pagination 翻页器

## 何时使用

- 在有大量内容展现需要进行分页加载处理的时候；

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/pagination-v2?isRenderNav=false" />
## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'total',
      type: 'number',
      defaul: '100',
      desc: '总记录数',
    },
    {
      code: 'current',
      type: 'number',
      default: '1',
      desc: '当前页码',
    },
    {
      code: 'size',
      type: `'small' | 'medium' | 'large'`,
      default: `'medium'`,
      desc: '分页尺寸',
    },
    {
      code: 'type',
      type: `'normal' | 'simple' | 'mini'`,
      default: `'normal'`,
      desc: '分页类型',
    },
    {
      code: 'shape',
      type: `'normal' | 'arrow-only' | 'arrow-prev-only' | 'no-border'`,
      default: `'normal'`,
      desc: '前进后退按钮样式',
    },
    {
      code: 'pageSizeSelector',
      type: `false | 'filter' | 'dropdown'`,
      default: 'false',
      desc: '每页显示选择器类型',
    },
    {
      code: 'pageSizeList',
      type: `string`,
      default: "'5,10,20'",
      desc: '每页显示选择器可选值',
    },
    {
      code: 'pageSize',
      type: 'number',
      default: '10',
      desc: '每页展示条数',
    },
    {
      code: 'pageSizePosition',
      type: `'start' | 'end'`,
      default: `'end'`,
      desc: '每页显示选择器在组件中的位置',
    },
    {
      code: 'hideOnlyOnePage',
      type: 'boolean',
      default: 'false',
      desc: '当分页数为 1 时，是否隐藏分页器',
    },
    {
      code: 'showJump',
      type: 'boolean',
      default: 'true',
      desc: '显示跳转输入框与按钮，当**type**为normal时，且在页码数超过5页后起作用',
    },
    {
      code: 'onPageSizeChange',
      type: '(pageSize: number) => void',
      default: '-',
      desc: '当每页数量改变时',
    },
    {
      code: 'onChange',
      type: '(currentPagination: number) => void',
      default: '-',
      desc: '当页码改变时',
    },
  ]}
/>