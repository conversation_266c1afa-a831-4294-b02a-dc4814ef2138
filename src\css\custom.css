/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-font-size-base: 14px;
  --ifm-color-primary: #0089ff;
  --ifm-color-primary-dark: #007be6;
  --ifm-color-primary-darker: #0074d9;
  --ifm-color-primary-darkest: #0060b3;
  --ifm-color-primary-light: #1a95ff;
  --ifm-color-primary-lighter: #269bff;
  --ifm-color-primary-lightest: #4dacff;
  --ifm-code-font-size: 95%;
  --ifm-container-width-xl: 2000px;
  --aa-search-input-height: 32px;
  --ifm-font-family-base: -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif, BlinkMacSystemFont,
  'Segoe UI', Helvetica, Arial, sans-serif, 'Apple Color Emoji';
  --ifm-font-family-monospace: Menlo, Monaco, Consolas, "Courier New", monospace;
  --ifm-global-spacing: 1.5rem;
  --ifm-line-height-base: 1.85;
  /* --ifm-font-color-base: #333; */
}

.docusaurus-highlight-code-line {
  background-color: rgba(0, 0, 0, 0.1);
  display: block;
  margin: 0 calc(-1 * var(--ifm-pre-padding));
  padding: 0 var(--ifm-pre-padding);
}

html[data-theme='dark'] .docusaurus-highlight-code-line {
  background-color: rgba(0, 0, 0, 0.3);
}

html[mode='simple'] #__docusaurus>.navbar {
  display: none;
}

html[mode='simple'] .theme-doc-breadcrumbs {
  display: none;
}

html[mode='simple'] footer {
  display: none;
}

.navbar__logo,
.navbar__search {
  margin-right: 2rem;
}

.hero {
  padding: 5rem 0 !important;
  box-shadow: var(--ifm-navbar-shadow);
}

.homepage-content {
  max-width: 1400px;
  margin: 0 auto;
}

.heroBanner {
  padding: 4rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero__title{
  font-size: 2.4rem;
  background: -webkit-linear-gradient(315deg,#0089ff 25%,#30e724);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.pagination-nav__link {
  padding: 0.5rem 1.5rem;
}

.markdown h1:first-child {
  --ifm-h1-font-size: 2rem;
}

.markdown > h2{
  --ifm-h2-font-size: 1.5rem;
}

.markdown > h3{
  --ifm-h3-font-size: 1.25rem;
}

.markdown img {
  box-shadow: 9px 8px 20px 0px rgb(0 0 0 / 15%);
}

.homepage-content .contributors {
  padding: 0 100px;
}
