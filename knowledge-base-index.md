# 宜搭AI知识库快速索引

## 🔍 问题类型 → 文档映射

### API使用问题
**查阅文档**: `yida-api-reference.md`
**常见问题**:
- this指向错误 → 第3节"注意事项"
- 状态管理 → 第1节"全局变量管理"
- 组件操作 → 第2节"组件操作"
- 数据源调用 → 第3节"数据源操作"

### 组件配置问题
**查阅文档**: `yida-component-guide.md`
**常见问题**:
- 组件选择 → 对应组件分类章节
- 属性配置 → 各组件的"核心属性"部分
- 事件绑定 → "事件处理"部分
- 样式设置 → "样式设置规则"部分

### 开发流程问题
**查阅文档**: `yida-development-patterns.md`
**常见问题**:
- 项目规划 → 第1节"标准开发流程"
- 表单开发 → 第2节"表单开发模式"
- 报表开发 → 第2节"报表开发模式"
- 自定义页面 → 第2节"自定义页面开发模式"

### 代码质量问题
**查阅文档**: `yida-best-practices.md`
**常见问题**:
- 命名规范 → 第1节"代码质量实践"
- 性能优化 → 第2节"性能优化实践"
- 安全规范 → 第3节"安全开发实践"
- 用户体验 → 第4节"用户体验实践"

### 错误和调试问题
**查阅文档**: `yida-troubleshooting.md`
**常见问题**:
- 环境问题 → 第1节"开发环境问题"
- API调用失败 → 第2节"API调用问题"
- 组件异常 → 第3节"组件使用问题"
- 性能问题 → 第5节"性能优化问题"

## 📋 核心规则速查

### 状态管理规则
```javascript
// ✅ 正确
this.setState({ loading: true });

// ❌ 错误
this.state.loading = true;
```

### 组件操作规则
```javascript
// ✅ 正确
this.$('textField_name').getValue();
this.$('button_submit').set('disabled', true);

// ❌ 错误
this.$('textField_name').value;
this.$('button_submit').disabled = true;
```

### this指向规则
```javascript
// ✅ 正确 - 使用箭头函数
this.dataSourceMap.api.load((res) => {
  this.setState({ data: res });
});

// ❌ 错误 - function改变this指向
this.dataSourceMap.api.load(function(res) {
  this.setState({ data: res }); // this指向错误
});
```

## 🎯 常用代码模板

### 页面初始化模板
```javascript
export function didMount() {
  // 1. 初始化状态
  this.setState({
    loading: true,
    dataList: [],
    currentPage: 1
  });
  
  // 2. 加载初始数据
  this.loadInitialData();
}

export function loadInitialData() {
  this.dataSourceMap.getData.load()
    .then(response => {
      this.setState({
        dataList: response.data,
        loading: false
      });
    })
    .catch(error => {
      this.handleError(error, '数据加载');
    });
}
```

### 表单提交模板
```javascript
export function onSubmit() {
  // 1. 表单验证
  if (!this.validateForm()) {
    return;
  }
  
  // 2. 收集数据
  const formData = this.collectFormData();
  
  // 3. 提交数据
  this.submitData(formData);
}

export function validateForm() {
  const name = this.$('textField_name').getValue();
  if (!name) {
    this.utils.toast({
      type: 'error',
      title: '请输入姓名'
    });
    return false;
  }
  return true;
}
```

### 错误处理模板
```javascript
export function handleError(error, context = '') {
  console.error(`${context}失败:`, error);
  
  let message = '操作失败，请重试';
  if (error.code === 'NETWORK_ERROR') {
    message = '网络连接失败，请检查网络';
  }
  
  this.utils.toast({
    type: 'error',
    title: message
  });
}
```

## 🔧 调试检查清单

### 代码生成前检查
- [ ] 是否使用正确的API语法
- [ ] 是否遵循状态管理规则
- [ ] 是否处理了this指向问题
- [ ] 是否包含错误处理
- [ ] 是否符合命名规范

### 功能实现后检查
- [ ] 组件配置是否正确
- [ ] 事件绑定是否生效
- [ ] 数据流是否正常
- [ ] 错误情况是否处理
- [ ] 用户体验是否友好

## 📚 文档优先级

### 高优先级（必读）
1. `yida-core-rules.md` - 基础规则，必须遵循
2. `yida-api-reference.md` - API使用，日常必需

### 中优先级（常用）
3. `yida-component-guide.md` - 组件使用指南
4. `yida-troubleshooting.md` - 问题解决方案

### 低优先级（参考）
5. `yida-development-patterns.md` - 开发模式参考
6. `yida-best-practices.md` - 最佳实践指导

## 🎯 使用建议

### 对于AI助手
1. **问题分析**: 先确定问题类型，再查阅对应文档
2. **代码生成**: 严格按照规则和模板生成代码
3. **质量检查**: 使用检查清单验证代码质量
4. **持续学习**: 根据反馈不断优化回答质量

### 对于开发者
1. **学习路径**: 按优先级顺序学习文档
2. **实践应用**: 结合实际项目使用知识库
3. **问题反馈**: 及时反馈使用中的问题
4. **知识更新**: 关注平台更新，及时同步知识

---

**💡 提示**: 这个索引文件可以帮助快速定位所需信息，建议收藏备用！
