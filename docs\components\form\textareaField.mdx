---
title: TextareaField 多行输入框
order: 11
---

# TextareaField 多行输入框

## 何时使用

- 用于大量文字输入输入场景，可是区域比文本输入框大；

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/textarea-field-v2?isRenderNav=false" />

## 组件属性

import AttrTable from "components/AttrTable";

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'value',
      type: 'string',
      default: `-`,
      desc: '当前组件默认值',
    },
    {
      code: 'placeholder',
      type: 'string',
      default: '请输入',
      desc: '表单组件占位提示信息',
    },
    {
      code: 'state',
      type: `'error' | 'loading' | 'success' | '' `,
      default: `''`,
      desc: '设置输入框state状态',
    },
    {
      code: 'rows',
      type: 'number',
      default: '4',
      desc: '设置多行文本高度，仅 PC 端有效',
    },
    {
      code: 'hasLimitHint',
      type: 'boolean',
      default: 'false',
      desc: '是否展示多行文本计数器',
    },
    {
      code: 'maxLength',
      type: 'number',
      default: '-',
      desc: '多行文本字数上限',
    },
    {
      code: 'autoHeight',
      type: 'boolean',
      default: 'false',
      desc: '是否开启多行输入框自动高度',
    },
    {
      code: 'trim',
      type: 'boolean',
      default: 'false',
      desc: '是否开启自动去除头尾空字符',
    },
    {
      code: 'autoFocus',
      type: 'boolean',
      default: 'false',
      desc: '是否开启自动聚焦',
    },
    {
      code: 'onChange',
      type: '({ value: string }) => void',
      default: '',
      desc: '组件值发生改变事件',
    },
    {
      code: 'hasClear',
      type: 'boolean',
      default: 'true',
      desc: '是否开启多行文本清除按钮',
    },
    {
      code: 'addonBefore',
      type: 'string',
      default: `-`,
      desc: '多行输入框前附加内容',
    },
    {
      code: 'addonAfter',
      type: 'string',
      default: `-`,
      desc: '输入框后附加内容',
    },
    {
      code: 'cutString',
      type: 'boolean',
      default: 'false',
      desc: '当超出maxlength时截断超出字符串',
    },
  ]}
/>
