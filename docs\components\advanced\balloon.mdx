---
title: Balloon 气泡提示
order: 7
---

# Balloon 气泡提示

## 何时使用

- 当用户与被说明对象（文字，图片，输入框等）发生交互行为的 action 开始时, 即刻跟随动作出现一种辅助或帮助的提示信息。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/balloon-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'TYPE',
      type: " 'balloon' | 'tooltip' ",
      default: "'balloon'",
      desc: '设置气泡类型，当气泡类型选择为 **tooltip** 时直接展示文字',
    },
    {
      code: 'type',
      type: " 'normal' | 'primary' ",
      default: "'normal'",
      desc: '气泡的样式类型',
    },
    {
      code: 'content',
      type: 'string',
      default: "'提示内容'",
      desc: '气泡组件显示内容',
    },
    {
      code: 'triggerType',
      type: "'hover' | 'click' | 'focus'",
      default: "'click'",
      desc: '用于控制气泡组件的触发条件',
    },
    {
      code: 'closable',
      type: 'boolean',
      default: 'true',
      desc: '是否显示关闭按钮',
    },
    {
      code: 'defaultVisible',
      type: 'boolean',
      default: 'false',
      desc: '组件初始化时是否为显示状态',
    },
    {
      code: 'delay',
      type: 'number',
      default: '0',
      desc: '时气泡显示的延迟时间，仅触发行为为hover时生效，单位为ms',
    },
    {
      code: 'align',
      type: "'t' | 'b' | 'l' | 'r' | 'tl' | 'tr' | 'bl' | 'br' | 'lt' | 'lb' | 'rt' | 'rb'",
      default: "'b'",
      desc: '气泡弹出层的显示位置',
    },
    {
      code: 'display',
      type: " 'inline-block' | 'block' ",
      default: "'inline-block'",
      desc: '用于配置触发元素所占空间',
    },
    {
      code: 'overlayMaxWidth',
      type: 'string',
      default: "'300px'",
      desc: "用于控制浮层最大宽度，**'initial'**表示不限制宽度",
    },
    {
      code: 'balloonOverlayVisible',
      type: 'boolean',
      default: 'true',
      desc: '设计器中是否显示弹层，该配置项只在设计器中有效',
    },
    {
      code: 'onClose',
      type: '() => void',
      default: '-',
      desc: 'Balloon 浮层关闭时触发的事件',
    },
    {
      code: 'afterClose',
      type: '() => void',
      default: '-',
      desc: 'Balloon 浮层关闭后触发的事件, 如果有动画，则在动画结束后触发',
    },
    {
      code: 'onVisibleChange',
      type: '(visible: boolean) => void',
      default: '-',
      desc: 'Balloon 浮层关闭后触发的事件, 如果有动画，则在动画结束后触发',
    },
  ]}
/>
