# 宜搭知识库RAG系统实施指南

## 🎯 RAG系统概述

RAG（Retrieval-Augmented Generation）系统可以让AI动态检索知识库内容，实现更精准的回答。

## 🏗️ 系统架构

```mermaid
graph TD
    A[用户问题] --> B[问题分析]
    B --> C[向量检索]
    C --> D[知识库文档]
    D --> E[相关内容提取]
    E --> F[上下文构建]
    F --> G[AI生成回答]
    G --> H[答案输出]
```

## 📚 实施方案

### 方案一：向量数据库方案

#### 1. 文档预处理
```python
# 文档分块处理
def chunk_documents():
    chunks = []
    
    # 按章节分块
    for doc in knowledge_base_docs:
        sections = split_by_headers(doc)
        for section in sections:
            chunks.append({
                'content': section.content,
                'metadata': {
                    'source': doc.filename,
                    'section': section.title,
                    'type': section.type  # rule/api/component/pattern
                }
            })
    
    return chunks

# 生成向量嵌入
def generate_embeddings(chunks):
    embeddings = []
    for chunk in chunks:
        vector = embedding_model.encode(chunk['content'])
        embeddings.append({
            'vector': vector,
            'metadata': chunk['metadata'],
            'content': chunk['content']
        })
    return embeddings
```

#### 2. 检索系统
```python
def retrieve_relevant_content(query, top_k=5):
    # 查询向量化
    query_vector = embedding_model.encode(query)
    
    # 相似度搜索
    results = vector_db.similarity_search(
        query_vector, 
        top_k=top_k,
        filter={'type': determine_query_type(query)}
    )
    
    return results

def determine_query_type(query):
    """根据问题确定查询类型"""
    if 'API' in query or 'this.' in query:
        return 'api'
    elif '组件' in query or 'component' in query:
        return 'component'
    elif '规则' in query or '约束' in query:
        return 'rule'
    else:
        return 'general'
```

#### 3. 上下文构建
```python
def build_context(query, retrieved_docs):
    context = f"""
基于以下宜搭开发知识库内容回答问题：

问题：{query}

相关知识：
"""
    
    for doc in retrieved_docs:
        context += f"""
## {doc.metadata['section']}
{doc.content}

"""
    
    context += """
请基于上述知识库内容，严格遵循宜搭开发规则，提供准确的解答。
"""
    
    return context
```

### 方案二：关键词匹配方案

#### 1. 关键词索引构建
```python
# 构建关键词索引
keyword_index = {
    'api': {
        'setState': 'yida-api-reference.md#全局变量管理',
        'getValue': 'yida-api-reference.md#组件操作',
        'dataSourceMap': 'yida-api-reference.md#数据源操作',
        'utils.toast': 'yida-api-reference.md#工具类函数'
    },
    'component': {
        'Button': 'yida-component-guide.md#基础组件',
        'TextField': 'yida-component-guide.md#表单组件',
        'Table': 'yida-component-guide.md#高级组件',
        'Container': 'yida-component-guide.md#布局组件'
    },
    'rule': {
        'this指向': 'yida-core-rules.md#this指向问题处理',
        '状态管理': 'yida-core-rules.md#全局变量管理',
        '组件操作': 'yida-core-rules.md#组件操作规则'
    },
    'problem': {
        '安装失败': 'yida-troubleshooting.md#开发环境问题',
        'API调用失败': 'yida-troubleshooting.md#API调用问题',
        '性能问题': 'yida-troubleshooting.md#性能优化问题'
    }
}
```

#### 2. 智能检索
```python
def smart_retrieve(query):
    relevant_sections = []
    
    # 关键词匹配
    for category, keywords in keyword_index.items():
        for keyword, doc_path in keywords.items():
            if keyword.lower() in query.lower():
                relevant_sections.append({
                    'category': category,
                    'keyword': keyword,
                    'doc_path': doc_path,
                    'priority': calculate_priority(keyword, query)
                })
    
    # 按优先级排序
    relevant_sections.sort(key=lambda x: x['priority'], reverse=True)
    
    return relevant_sections[:3]  # 返回前3个最相关的
```

## 🔧 技术实现选项

### 选项1：使用LangChain
```python
from langchain.document_loaders import DirectoryLoader
from langchain.text_splitter import MarkdownHeaderTextSplitter
from langchain.vectorstores import Chroma
from langchain.embeddings import OpenAIEmbeddings

# 加载文档
loader = DirectoryLoader('./knowledge-base/', glob="*.md")
documents = loader.load()

# 分割文档
splitter = MarkdownHeaderTextSplitter(
    headers_to_split_on=[
        ("#", "Header 1"),
        ("##", "Header 2"),
        ("###", "Header 3"),
    ]
)

# 创建向量存储
vectorstore = Chroma.from_documents(
    documents=documents,
    embedding=OpenAIEmbeddings(),
    persist_directory="./chroma_db"
)

# 检索器
retriever = vectorstore.as_retriever(
    search_type="similarity",
    search_kwargs={"k": 5}
)
```

### 选项2：使用简单的文本匹配
```python
import re
import os

class SimpleKnowledgeRetriever:
    def __init__(self, knowledge_base_path):
        self.docs = self.load_documents(knowledge_base_path)
        self.index = self.build_index()
    
    def load_documents(self, path):
        docs = {}
        for filename in os.listdir(path):
            if filename.endswith('.md'):
                with open(os.path.join(path, filename), 'r', encoding='utf-8') as f:
                    docs[filename] = f.read()
        return docs
    
    def build_index(self):
        index = {}
        for filename, content in self.docs.items():
            # 提取标题和关键词
            headers = re.findall(r'^#+\s+(.+)$', content, re.MULTILINE)
            code_blocks = re.findall(r'```[\s\S]*?```', content)
            
            index[filename] = {
                'headers': headers,
                'code_blocks': code_blocks,
                'content': content
            }
        return index
    
    def retrieve(self, query):
        relevant_docs = []
        
        for filename, doc_info in self.index.items():
            score = 0
            
            # 标题匹配
            for header in doc_info['headers']:
                if any(word in header.lower() for word in query.lower().split()):
                    score += 2
            
            # 内容匹配
            if any(word in doc_info['content'].lower() for word in query.lower().split()):
                score += 1
            
            if score > 0:
                relevant_docs.append((filename, score))
        
        # 按分数排序
        relevant_docs.sort(key=lambda x: x[1], reverse=True)
        return relevant_docs[:3]
```

## 🚀 部署方案

### 方案1：本地部署
```bash
# 1. 安装依赖
pip install langchain chromadb openai

# 2. 构建向量数据库
python build_vectorstore.py

# 3. 启动检索服务
python rag_service.py
```

### 方案2：云端部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  rag-service:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./knowledge-base:/app/knowledge-base
      - ./chroma_db:/app/chroma_db
```

### 方案3：API集成
```python
# FastAPI服务
from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI()

class QueryRequest(BaseModel):
    question: str
    context_type: str = "general"

@app.post("/retrieve")
async def retrieve_knowledge(request: QueryRequest):
    # 检索相关知识
    relevant_docs = retriever.retrieve(request.question)
    
    # 构建上下文
    context = build_context(request.question, relevant_docs)
    
    return {
        "context": context,
        "sources": [doc.metadata for doc in relevant_docs]
    }
```

## 📊 效果评估

### 评估指标
```python
def evaluate_rag_system():
    test_cases = [
        {
            "query": "如何正确使用setState",
            "expected_docs": ["yida-core-rules.md", "yida-api-reference.md"],
            "expected_content": ["this.setState", "全局变量管理"]
        },
        {
            "query": "Button组件怎么配置",
            "expected_docs": ["yida-component-guide.md"],
            "expected_content": ["Button", "按钮", "核心属性"]
        }
    ]
    
    for case in test_cases:
        results = retrieve_relevant_content(case["query"])
        
        # 计算准确率
        precision = calculate_precision(results, case["expected_docs"])
        recall = calculate_recall(results, case["expected_content"])
        
        print(f"Query: {case['query']}")
        print(f"Precision: {precision:.2f}")
        print(f"Recall: {recall:.2f}")
```

## 💡 使用建议

### 对于开发者
1. **选择合适方案**: 根据技术栈和需求选择实施方案
2. **数据质量**: 确保知识库文档的质量和更新
3. **持续优化**: 根据使用反馈不断优化检索效果
4. **监控评估**: 定期评估系统效果并调整参数

### 对于AI系统
1. **上下文管理**: 合理控制上下文长度
2. **相关性过滤**: 只使用高相关度的检索结果
3. **来源标注**: 明确标注信息来源
4. **错误处理**: 处理检索失败的情况

---

**🎯 总结**: RAG系统可以显著提升AI使用知识库的效果，建议根据实际需求选择合适的实施方案。
