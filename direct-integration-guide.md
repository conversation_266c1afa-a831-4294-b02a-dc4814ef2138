# 宜搭知识库直接集成使用指南

## 🎯 概述
本指南提供了让AI直接使用宜搭知识库的多种实用方法，适用于不同的AI平台和使用场景。

## 📋 方法总览

### 方法对比表

| 方法 | 技术难度 | 实施成本 | 效果 | 适用场景 |
|------|----------|----------|------|----------|
| 上下文注入 | 低 | 低 | 高 | 对话式AI、ChatGPT等 |
| RAG系统 | 高 | 高 | 很高 | 企业级AI应用 |
| 文件上传 | 低 | 低 | 中 | 支持文件上传的AI |
| API集成 | 中 | 中 | 高 | 自定义AI应用 |
| 训练微调 | 很高 | 很高 | 很高 | 专业AI模型 |

## 🚀 方法一：上下文注入（推荐）

### 适用平台
- ChatGPT (GPT-4/3.5)
- Claude
- 文心一言
- 通义千问
- 其他对话式AI

### 实施步骤

#### 1. 准备提示词模板
```markdown
# 系统提示词
你是宜搭低代码开发专家。在回答任何宜搭相关问题时，必须严格遵循以下开发规则：

## 核心规则
- 状态管理：必须使用 this.setState() 修改全局变量
- 组件操作：使用 get(), set(), getValue(), setValue() 等API方法
- this指向：注意嵌套函数中的this指向问题
- 错误处理：实现完善的异常处理

## API规范
✅ 正确用法：
- this.setState({key: value})
- this.$('fieldId').get('prop')
- this.dataSourceMap.apiName.load()

❌ 禁止用法：
- this.state.key = value
- this.$('fieldId').prop
```

#### 2. 动态内容注入
```javascript
// 根据问题类型注入相关知识
function injectRelevantKnowledge(userQuestion) {
    let context = basePrompt;
    
    if (userQuestion.includes('API') || userQuestion.includes('this.')) {
        context += getApiReference();
    }
    
    if (userQuestion.includes('组件') || userQuestion.includes('component')) {
        context += getComponentGuide();
    }
    
    if (userQuestion.includes('错误') || userQuestion.includes('问题')) {
        context += getTroubleshootingGuide();
    }
    
    return context + `\n\n用户问题：${userQuestion}`;
}
```

### 使用示例

#### ChatGPT使用方式
```markdown
# 第一步：设置系统提示词
[复制 ai-prompt-template.md 的内容作为系统提示词]

# 第二步：提问时引用知识库
用户：我想创建一个用户列表页面，应该怎么做？

AI：基于宜搭开发规则，我来为您设计用户列表页面...
[AI会根据知识库规则生成符合规范的代码]
```

#### Claude使用方式
```markdown
# 在对话开始时说明
请作为宜搭低代码开发专家，严格遵循以下知识库规则：
[粘贴核心规则内容]

然后提出具体问题...
```

## 🔧 方法二：文件上传方式

### 适用平台
- ChatGPT Plus (支持文件上传)
- Claude (支持文档上传)
- 其他支持文件上传的AI平台

### 实施步骤

#### 1. 准备知识库文件包
```bash
# 创建知识库压缩包
zip yida-knowledge-base.zip \
    yida-core-rules.md \
    yida-api-reference.md \
    yida-component-guide.md \
    knowledge-base-index.md
```

#### 2. 上传并激活
```markdown
# 上传文件后的激活提示词
我已上传宜搭低代码开发知识库文件，请：
1. 仔细阅读所有文档内容
2. 理解宜搭开发规则和约束
3. 在后续回答中严格遵循这些规则
4. 基于知识库内容提供准确的开发指导

请确认您已理解知识库内容，然后我开始提问。
```

### 使用技巧
```markdown
# 提问时引用具体文档
请参考 yida-api-reference.md 中的API使用规范，帮我实现...

# 要求检查规范性
请检查以下代码是否符合 yida-core-rules.md 中的开发规则...

# 要求提供最佳实践
基于 yida-best-practices.md，请优化以下代码...
```

## 🌐 方法三：API集成方式

### 适用场景
- 自定义AI应用
- 企业内部AI助手
- 集成到开发工具中

### 实施架构
```javascript
// 知识库API服务
class YidaKnowledgeAPI {
    constructor() {
        this.knowledgeBase = this.loadKnowledgeBase();
    }
    
    // 根据问题检索相关知识
    async retrieveKnowledge(question) {
        const relevantDocs = this.searchRelevantDocs(question);
        const context = this.buildContext(relevantDocs);
        return context;
    }
    
    // 验证代码是否符合规范
    async validateCode(code) {
        const rules = this.getRules();
        const violations = this.checkViolations(code, rules);
        return {
            isValid: violations.length === 0,
            violations: violations
        };
    }
    
    // 获取组件使用建议
    async getComponentAdvice(componentType) {
        const guide = this.getComponentGuide(componentType);
        return guide;
    }
}

// 使用示例
const knowledgeAPI = new YidaKnowledgeAPI();

// AI调用知识库
async function generateYidaCode(userRequest) {
    // 1. 检索相关知识
    const context = await knowledgeAPI.retrieveKnowledge(userRequest);
    
    // 2. 生成代码
    const code = await aiModel.generate(context + userRequest);
    
    // 3. 验证代码
    const validation = await knowledgeAPI.validateCode(code);
    
    if (!validation.isValid) {
        // 修正代码
        const correctedCode = await aiModel.correct(code, validation.violations);
        return correctedCode;
    }
    
    return code;
}
```

## 📱 方法四：移动端集成

### 钉钉小程序集成
```javascript
// 在钉钉小程序中集成知识库
Page({
    data: {
        knowledgeBase: null
    },
    
    onLoad() {
        this.loadKnowledgeBase();
    },
    
    // 加载知识库
    async loadKnowledgeBase() {
        const kb = await dd.httpRequest({
            url: 'https://your-domain.com/api/yida-knowledge-base',
            method: 'GET'
        });
        
        this.setData({
            knowledgeBase: kb.data
        });
    },
    
    // AI助手对话
    async chatWithAI(userMessage) {
        const context = this.buildContextFromKB(userMessage);
        
        const response = await dd.httpRequest({
            url: 'https://api.openai.com/v1/chat/completions',
            method: 'POST',
            data: {
                model: 'gpt-4',
                messages: [
                    { role: 'system', content: context },
                    { role: 'user', content: userMessage }
                ]
            }
        });
        
        return response.data.choices[0].message.content;
    }
});
```

## 🔍 方法五：IDE插件集成

### VS Code插件示例
```javascript
// extension.js
const vscode = require('vscode');
const YidaKnowledgeBase = require('./yida-knowledge-base');

function activate(context) {
    const kb = new YidaKnowledgeBase();
    
    // 代码补全提供器
    const completionProvider = vscode.languages.registerCompletionItemProvider(
        'javascript',
        {
            provideCompletionItems(document, position) {
                const line = document.lineAt(position);
                const text = line.text;
                
                // 检测宜搭API调用
                if (text.includes('this.')) {
                    return kb.getApiCompletions(text);
                }
                
                return [];
            }
        },
        '.' // 触发字符
    );
    
    // 代码检查
    const diagnosticCollection = vscode.languages.createDiagnosticCollection('yida');
    
    function validateDocument(document) {
        const diagnostics = [];
        const text = document.getText();
        
        // 检查是否违反宜搭开发规则
        const violations = kb.checkViolations(text);
        
        violations.forEach(violation => {
            const diagnostic = new vscode.Diagnostic(
                violation.range,
                violation.message,
                vscode.DiagnosticSeverity.Error
            );
            diagnostics.push(diagnostic);
        });
        
        diagnosticCollection.set(document.uri, diagnostics);
    }
    
    context.subscriptions.push(completionProvider, diagnosticCollection);
}
```

## 📊 效果监控

### 使用效果评估
```javascript
// 效果评估指标
const evaluationMetrics = {
    // 准确性指标
    accuracy: {
        correctApiUsage: 0.95,      // API使用正确率
        ruleCompliance: 0.98,       // 规则遵循率
        codeQuality: 0.90           // 代码质量分数
    },
    
    // 效率指标
    efficiency: {
        responseTime: 2.5,          // 平均响应时间(秒)
        problemSolveRate: 0.85,     // 问题解决率
        userSatisfaction: 4.2       // 用户满意度(1-5分)
    },
    
    // 使用指标
    usage: {
        dailyQueries: 150,          // 日均查询次数
        knowledgeHitRate: 0.78,     // 知识库命中率
        repeatQuestionRate: 0.15    // 重复问题率
    }
};

// 监控代码
function monitorUsage(query, response, userFeedback) {
    // 记录查询日志
    logQuery({
        timestamp: new Date(),
        query: query,
        response: response,
        feedback: userFeedback,
        knowledgeUsed: extractKnowledgeUsed(response)
    });
    
    // 更新指标
    updateMetrics(query, response, userFeedback);
}
```

## 💡 最佳实践建议

### 1. 选择合适的方法
```markdown
- **快速验证**: 使用上下文注入方法
- **日常使用**: 使用文件上传方法
- **企业应用**: 使用API集成方法
- **开发工具**: 使用IDE插件方法
```

### 2. 优化使用效果
```markdown
- **精确提问**: 提供具体的问题描述
- **上下文管理**: 合理控制上下文长度
- **反馈循环**: 根据使用效果调整策略
- **持续更新**: 定期更新知识库内容
```

### 3. 常见问题处理
```markdown
- **响应不准确**: 检查提示词设置和知识库内容
- **性能问题**: 优化检索算法和缓存策略
- **集成困难**: 选择更简单的集成方式
- **维护成本**: 建立自动化更新机制
```

---

## 🎯 总结

根据不同的使用场景和技术能力，可以选择合适的方法让AI使用宜搭知识库：

1. **初学者**: 推荐使用上下文注入或文件上传方法
2. **开发者**: 推荐使用API集成或IDE插件方法
3. **企业用户**: 推荐使用RAG系统或自定义集成方案

选择合适的方法，可以显著提升AI在宜搭低代码开发中的表现！
