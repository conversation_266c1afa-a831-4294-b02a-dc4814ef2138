# 钉钉低代码体系名词解释

* **全局变量** - 用于管理页面状态并控制页面的重新渲染，类似 React 中的 state；
* **远程 API** - 用于描述服务端的 HTTP 接口的系列配置，包含请求地址、参数、数据处理函数等；
* **Schema** - 低代码协议：用于描述低代码相关的组成部分，如页面/组件等等，类似于 HTML。
* **组件唯一标识** - 宜搭会为每个组件设置一个全局唯一标识，用于识别组件实例（相当于 DOM id），组件唯一标识可以通过组件属性面板进行查看；因表单组件的唯一标识的更改，会影响数据库存储（模型可能不同，会导致数据对不上的情况），为了不让普通用户误操作，我们提供了 [Schema 编辑模式](/docs/guide/concept/debug#开启-schema-工作台)，当开发者需要的时候，可以小心使用；
* **页面** - 一个独立的展示界面，宜搭中搭建的主要实体，通过工作台可以创建不同类型的宜搭页面，例如：表单、报表、自定义页面等；
* **物料** - 能够被沉淀下来直接使用的前端能力，一般表现为业务组件、区块、模板。
* **业务组件（Business Component）** - 业务领域内基于基础组件之上定义的组件，可能会包含特定业务域的交互或者是业务数据，对外仅暴露可配置的属性，且必须发布到公域（如阿里 NPM）；在同一个业务域内可以流通，但不需要确保可以跨业务域复用。
* **低代码业务组件（Low-Code Business Component）** - 通过低代码编辑器搭建而来，有别于源码开发的业务组件，属于业务组件中的一种类型，遵循业务组件的定义；同时低代码业务组件还可以通过低代码编辑器继续多次编辑。
* **区块（Block）** - 通过低代码搭建的方式，将一系列业务组件、布局组件进行嵌套组合而成，不对外提供可配置的属性。可通过区块容器组件的包裹，实现区块内部具备有完整的样式、事件、生命周期管理、状态管理、数据流转机制。能独立存在和运行，可通过复制 schema 实现跨页面、跨应用的快速复用，保障功能和数据的正常。
* **模板（Template）** - 特定垂直业务领域内的业务组件、区块可组合为单个页面，或者是再配合路由组合为多个页面集，统称为模板。
