# 宜搭低代码开发核心规则

## 🎯 概述
本文档定义了宜搭低代码平台开发的核心规则、约束条件和最佳实践，AI助手在进行宜搭相关开发时必须严格遵循这些规则。

## 📋 基本开发原则

### 1. 平台特性理解
- **低代码平台**: 宜搭是基于阿里自研 Low-Code Engine 的企业级低代码平台
- **双端适配**: 一次开发，移动端和PC端自动适配
- **MVVM模式**: 采用类似React的状态管理模式
- **组件化开发**: 通过拖拽组件和配置属性实现功能

### 2. 开发模式
- **可视化优先**: 优先使用拖拽和配置，减少代码编写
- **渐进式增强**: 从简单配置到复杂JS逻辑的渐进式开发
- **数据驱动**: 通过数据绑定和状态管理驱动界面更新

## 🔧 核心API使用规则

### 1. 全局变量管理 (State Management)

#### ✅ 正确用法
```javascript
// 读取全局变量
const status = this.state.status;
const name = this.state.name;

// 设置全局变量 - 必须使用setState
this.setState({
  status: 'loading',
  text: '加载中...'
});

// 批量更新
this.setState({
  user: { name: '张三', age: 25 },
  loading: false,
  error: null
});
```

#### ❌ 禁止用法
```javascript
// 禁止直接修改state
this.state.status = 'loading';  // ❌ 错误！
this.state.user.name = '李四';   // ❌ 错误！

// 禁止使用未定义的变量
const data = this.state.undefinedVar; // ❌ 可能出错
```

#### 📝 规则说明
- **必须使用 `this.setState()`** 修改全局变量
- **禁止直接赋值** `this.state.xxx = value`
- **支持批量更新** 一次setState可以更新多个变量
- **自动触发重渲染** setState会自动触发页面重新渲染

### 2. 组件操作规则

#### ✅ 正确的组件API使用
```javascript
// 获取组件实例
const component = this.$('textField_kyz78exp');

// 获取组件值
const value = this.$('textField_kyz78exp').getValue();

// 设置组件值
this.$('textField_kyz78exp').setValue('新值');

// 获取组件属性 - 使用get方法
const content = this.$('text_kyz78exo').get('content');

// 设置组件属性
this.$('button_abc123').set('disabled', true);

// 显示/隐藏组件
this.$('container_xyz').show();
this.$('container_xyz').hide();
```

#### ❌ 禁止用法
```javascript
// 禁止直接访问组件属性
const content = this.$('text_kyz78exo').content; // ❌ 错误！

// 禁止直接设置属性
this.$('text_kyz78exo').content = 'new content'; // ❌ 错误！
```

#### 📝 规则说明
- **组件标识符**: 每个组件都有唯一的fieldId标识符
- **必须使用API方法**: 通过 `get()`, `set()`, `getValue()`, `setValue()` 等方法操作
- **禁止直接属性访问**: 不能直接访问或修改组件属性

### 3. this指向问题处理

#### ✅ 正确处理this指向
```javascript
// 在事件函数最外层，this指向正确
export function onClick() {
  const status = this.state.status;
  this.setState({ status: status + 1 });
}

// 嵌套函数中保存this引用
export function fetchData() {
  const that = this;
  this.dataSourceMap.getList.load(function(ret) {
    that.setState({ data: ret }); // 使用保存的引用
  });
}

// 推荐：使用箭头函数避免this指向问题
export function fetchDataBetter() {
  this.dataSourceMap.getList.load((ret) => {
    this.setState({ data: ret }); // 箭头函数保持this指向
  });
}
```

#### ❌ 错误的this使用
```javascript
// 错误：嵌套函数中this指向改变
export function fetchData() {
  this.dataSourceMap.getList.load(function(ret) {
    this.setState({ data: ret }); // ❌ this指向已改变
  });
}
```

#### 📝 规则说明
- **最外层函数**: this指向正确的执行上下文
- **嵌套函数**: function关键字会改变this指向
- **解决方案**: 使用箭头函数或保存this引用

## 🎨 组件使用规则

### 1. 组件配置原则
- **属性优先**: 优先通过属性面板配置组件功能
- **数据绑定**: 使用变量绑定实现动态内容
- **事件处理**: 通过动作绑定实现交互逻辑

### 2. 组件唯一标识规则
- **自动生成**: 系统自动为每个组件生成唯一标识
- **命名规范**: 格式为 `组件类型_随机字符串`，如 `textField_kyz78exp`
- **谨慎修改**: 表单组件的标识符修改会影响数据库存储

### 3. 组件层级关系
- **容器组件**: 用于布局和组织其他组件
- **表单组件**: 用于数据输入和收集
- **展示组件**: 用于数据展示和信息呈现
- **交互组件**: 用于用户操作和事件触发

## 📊 数据处理规则

### 1. 数据绑定规则
```javascript
// 变量绑定语法（在属性配置中）
state.variableName        // 绑定全局变量
state.user.name          // 绑定对象属性
state.list[0].title      // 绑定数组元素

// 表达式绑定
state.count > 0 ? '有数据' : '无数据'
`Hello ${state.userName}!`
```

### 2. 远程API调用规则
```javascript
// 手动调用远程API
export function loadData() {
  this.dataSourceMap.getDataList.load({
    pageSize: 10,
    page: this.state.currentPage
  }).then((res) => {
    this.setState({ dataList: res.data });
  }).catch((err) => {
    this.utils.toast({
      type: 'error',
      title: '请求失败'
    });
  });
}

// 重新加载所有自动加载的API
export function refreshAll() {
  this.reloadDataSource().then(() => {
    this.utils.toast({
      type: 'success',
      title: '刷新成功'
    });
  });
}
```

## 🔒 安全和约束规则

### 1. 代码安全规范
- **输入验证**: 对用户输入进行验证和过滤
- **XSS防护**: 避免直接输出用户输入内容
- **权限控制**: 遵循最小权限原则

### 2. 性能约束
- **避免频繁setState**: 批量更新状态而非频繁调用
- **合理使用远程API**: 避免不必要的网络请求
- **组件数量控制**: 单页面组件数量不宜过多

### 3. 兼容性要求
- **浏览器兼容**: 支持主流浏览器
- **移动端适配**: 确保移动端正常显示和交互
- **版本兼容**: 遵循平台API版本要求

## 📝 事件处理规范

### 1. 事件绑定规则
```javascript
// 标准事件处理函数
export function onClick() {
  // 获取事件参数
  const { name, age } = this.params;
  
  // 处理业务逻辑
  this.setState({
    message: `Hello ${name}, you are ${age} years old!`
  });
}

// 表单提交事件
export function onSubmit() {
  // 获取表单数据
  const formData = {
    name: this.$('textField_name').getValue(),
    email: this.$('textField_email').getValue()
  };
  
  // 验证数据
  if (!formData.name) {
    this.utils.toast({
      type: 'error',
      title: '请输入姓名'
    });
    return;
  }
  
  // 提交数据
  this.dataSourceMap.submitForm.load(formData);
}
```

### 2. 事件参数处理
- **参数获取**: 通过 `this.params` 获取事件参数
- **参数验证**: 对参数进行必要的验证
- **错误处理**: 提供友好的错误提示

## 🔍 调试和错误处理

### 1. 调试方法
```javascript
// 使用console进行调试
export function debugFunction() {
  console.log('当前状态:', this.state);
  console.log('组件值:', this.$('textField_abc').getValue());
}

// 错误捕获
export function safeOperation() {
  try {
    // 可能出错的操作
    const result = this.someRiskyOperation();
    this.setState({ result });
  } catch (error) {
    console.error('操作失败:', error);
    this.utils.toast({
      type: 'error',
      title: '操作失败，请重试'
    });
  }
}
```

### 2. 错误处理原则
- **优雅降级**: 出错时提供备选方案
- **用户友好**: 提供清晰的错误提示
- **日志记录**: 记录错误信息便于排查

## 📚 最佳实践总结

### 1. 开发流程
1. **需求分析** → 确定功能需求和数据结构
2. **页面设计** → 拖拽组件完成界面布局
3. **数据建模** → 创建全局变量和数据源
4. **逻辑实现** → 编写事件处理和业务逻辑
5. **测试验证** → 预览测试功能完整性
6. **发布部署** → 保存并发布应用

### 2. 代码质量
- **命名规范**: 使用有意义的变量和函数名
- **注释完整**: 为复杂逻辑添加注释说明
- **结构清晰**: 保持代码结构清晰易读
- **错误处理**: 完善的错误处理机制

### 3. 用户体验
- **响应及时**: 操作反馈要及时
- **提示友好**: 错误提示要清晰友好
- **交互流畅**: 确保交互流程顺畅
- **性能优化**: 避免卡顿和延迟

---

**重要提醒**: 以上规则是宜搭低代码开发的核心约束，AI助手在生成代码和提供建议时必须严格遵循这些规则，确保代码的正确性和平台兼容性。
