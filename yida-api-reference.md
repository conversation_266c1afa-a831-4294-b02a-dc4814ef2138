# 宜搭 JS-API 完整参考手册

## 🎯 概述
本文档提供宜搭平台所有可用的JavaScript API的完整参考，包括详细的参数说明、使用示例和注意事项。

## 📚 API 分类索引

### 1. [全局变量管理](#全局变量管理)
- `this.state.xxx` - 读取全局变量
- `this.setState()` - 设置全局变量

### 2. [组件操作](#组件操作)
- `this.$(fieldId).get()` - 获取组件属性
- `this.$(fieldId).set()` - 设置组件属性
- `this.$(fieldId).getValue()` - 获取组件值
- `this.$(fieldId).setValue()` - 设置组件值
- `this.$(fieldId).show()` - 显示组件
- `this.$(fieldId).hide()` - 隐藏组件

### 3. [数据源操作](#数据源操作)
- `this.dataSourceMap.xxx.load()` - 调用远程API
- `this.reloadDataSource()` - 重新加载数据源

### 4. [工具类函数](#工具类函数)
- `this.utils.dialog()` - 对话框
- `this.utils.toast()` - 消息提示
- `this.utils.formatter()` - 格式化工具
- `this.utils.getLoginUserId()` - 获取用户ID
- `this.utils.isMobile()` - 判断移动端

### 5. [路由导航](#路由导航)
- `this.utils.router.push()` - 页面跳转
- `this.utils.router.replace()` - 页面替换
- `this.utils.router.getQuery()` - 获取URL参数

### 6. [函数调用](#函数调用)
- `this.methodName()` - 调用其他函数

---

## 全局变量管理

### this.state.xxx
**功能**: 获取全局变量的值  
**语法**: `this.state.variableName`  
**返回**: 变量值（任意类型）

```javascript
// 获取单个变量
const status = this.state.status;
const userName = this.state.userName;

// 获取对象属性
const userAge = this.state.user.age;
const firstItem = this.state.list[0];

// 获取URL参数（内置变量）
const pageId = this.state.urlParams.pageId;
```

### this.setState(object)
**功能**: 设置全局变量并触发页面重新渲染  
**参数**: 
- `object` - 要更新的变量对象
**返回**: void

```javascript
// 设置单个变量
this.setState({ status: 'loading' });

// 批量设置变量
this.setState({
  status: 'success',
  data: response.data,
  loading: false
});

// 更新对象属性
this.setState({
  user: {
    ...this.state.user,
    name: '新名称'
  }
});
```

**⚠️ 注意事项**:
- 必须使用 `setState` 修改变量，禁止直接赋值
- `setState` 会自动触发页面重新渲染
- 支持批量更新多个变量

---

## 组件操作

### this.$(fieldId).get(prop)
**功能**: 获取组件属性值  
**参数**:
- `fieldId` - 组件唯一标识
- `prop` - 属性名称
**返回**: 属性值

```javascript
// 获取文本组件内容
const content = this.$('text_abc123').get('content');

// 获取按钮标题
const title = this.$('button_xyz789').get('title');

// 获取组件显示状态
const visible = this.$('container_def456').get('visible');
```

### this.$(fieldId).set(prop, value)
**功能**: 设置组件属性值  
**参数**:
- `fieldId` - 组件唯一标识
- `prop` - 属性名称
- `value` - 属性值
**返回**: void

```javascript
// 设置文本内容
this.$('text_abc123').set('content', '新内容');

// 设置按钮禁用状态
this.$('button_xyz789').set('disabled', true);

// 设置组件样式
this.$('container_def456').set('style', { backgroundColor: '#f0f0f0' });
```

### this.$(fieldId).getValue()
**功能**: 获取表单组件的值  
**参数**:
- `fieldId` - 组件唯一标识
**返回**: 组件值

```javascript
// 获取输入框值
const inputValue = this.$('textField_abc123').getValue();

// 获取选择器值
const selectedValue = this.$('selectField_xyz789').getValue();

// 获取日期选择器值
const dateValue = this.$('dateField_def456').getValue();
```

### this.$(fieldId).setValue(value)
**功能**: 设置表单组件的值  
**参数**:
- `fieldId` - 组件唯一标识
- `value` - 要设置的值
**返回**: void

```javascript
// 设置输入框值
this.$('textField_abc123').setValue('新值');

// 设置选择器值
this.$('selectField_xyz789').setValue('option1');

// 设置日期值
this.$('dateField_def456').setValue(new Date());

// 清空值
this.$('textField_abc123').setValue('');
```

### this.$(fieldId).show() / hide()
**功能**: 显示或隐藏组件  
**参数**:
- `fieldId` - 组件唯一标识
**返回**: void

```javascript
// 显示组件
this.$('container_abc123').show();

// 隐藏组件
this.$('container_abc123').hide();

// 条件显示/隐藏
if (this.state.showDetails) {
  this.$('detailContainer').show();
} else {
  this.$('detailContainer').hide();
}
```

---

## 数据源操作

### this.dataSourceMap.xxx.load(params)
**功能**: 手动调用远程API  
**参数**:
- `params` - 请求参数对象（可选）
**返回**: Promise

```javascript
// 基本调用
export function loadData() {
  this.dataSourceMap.getUserList.load()
    .then(response => {
      this.setState({ userList: response.data });
    })
    .catch(error => {
      console.error('请求失败:', error);
    });
}

// 带参数调用
export function searchUsers() {
  this.dataSourceMap.getUserList.load({
    pageSize: 10,
    page: this.state.currentPage,
    keyword: this.state.searchKeyword
  }).then(response => {
    this.setState({ 
      userList: response.data,
      total: response.total 
    });
  });
}
```

### this.reloadDataSource()
**功能**: 重新加载所有自动加载的数据源  
**参数**: 无  
**返回**: Promise

```javascript
export function refreshPage() {
  this.reloadDataSource()
    .then(() => {
      this.utils.toast({
        type: 'success',
        title: '刷新成功'
      });
    })
    .catch(error => {
      this.utils.toast({
        type: 'error',
        title: '刷新失败'
      });
    });
}
```

---

## 工具类函数

### this.utils.dialog(options)
**功能**: 显示对话框  
**参数**: 配置对象
**返回**: 对话框实例（可调用hide()方法关闭）

```javascript
// 确认对话框
export function showConfirm() {
  this.utils.dialog({
    type: 'confirm',
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    onOk: () => {
      // 确认操作
      this.deleteRecord();
    },
    onCancel: () => {
      console.log('取消删除');
    }
  });
}

// 信息对话框
export function showInfo() {
  this.utils.dialog({
    type: 'alert',
    title: '提示',
    content: '操作完成！'
  });
}

// 手动关闭对话框
export function showCustomDialog() {
  const dialog = this.utils.dialog({
    title: '自定义对话框',
    content: '这是一个可以手动关闭的对话框',
    footer: false
  });
  
  // 3秒后自动关闭
  setTimeout(() => {
    dialog.hide();
  }, 3000);
}
```

**配置选项**:
- `type`: 'alert' | 'confirm' | 'show'
- `title`: 标题
- `content`: 内容（支持HTML/JSX）
- `onOk`: 确认回调
- `onCancel`: 取消回调
- `footer`: 是否显示底部按钮
- `hasMask`: 是否显示遮罩

### this.utils.toast(options)
**功能**: 显示消息提示  
**参数**: 配置对象
**返回**: 关闭函数（loading类型时可用）

```javascript
// 成功提示
export function showSuccess() {
  this.utils.toast({
    type: 'success',
    title: '操作成功！'
  });
}

// 错误提示
export function showError() {
  this.utils.toast({
    type: 'error',
    title: '操作失败，请重试'
  });
}

// 加载提示
export function showLoading() {
  const close = this.utils.toast({
    type: 'loading',
    title: '加载中...'
  });
  
  // 模拟异步操作
  setTimeout(() => {
    close(); // 关闭loading
    this.utils.toast({
      type: 'success',
      title: '加载完成'
    });
  }, 2000);
}
```

**配置选项**:
- `type`: 'success' | 'warning' | 'error' | 'notice' | 'help' | 'loading'
- `title`: 提示文本
- `size`: 'medium' | 'large'
- `duration`: 显示时长（loading类型无效）

### this.utils.formatter(type, value, format)
**功能**: 格式化数据  
**参数**:
- `type`: 格式化类型
- `value`: 要格式化的值
- `format`: 格式化参数
**返回**: 格式化后的字符串

```javascript
export function formatData() {
  // 格式化日期
  const date1 = this.utils.formatter('date', new Date(), 'YYYY-MM-DD');
  const date2 = this.utils.formatter('date', new Date(), 'YYYY/MM/DD HH:mm:ss');
  
  // 格式化金额
  const money = this.utils.formatter('money', '10000.99', ',');
  
  // 格式化手机号
  const phone = this.utils.formatter('cnmobile', '+8615652988282');
  
  // 格式化银行卡号
  const card = this.utils.formatter('card', '1565298828212233');
  
  console.log({ date1, date2, money, phone, card });
}
```

### this.utils.getLoginUserId()
**功能**: 获取当前登录用户ID  
**参数**: 无  
**返回**: 用户ID字符串

```javascript
export function getCurrentUser() {
  const userId = this.utils.getLoginUserId();
  console.log('当前用户ID:', userId);
}
```

### this.utils.getLoginUserName()
**功能**: 获取当前登录用户名称  
**参数**: 无  
**返回**: 用户名称字符串

```javascript
export function getCurrentUserName() {
  const userName = this.utils.getLoginUserName();
  console.log('当前用户名:', userName);
}
```

### this.utils.isMobile()
**功能**: 判断是否为移动端环境  
**参数**: 无  
**返回**: boolean

```javascript
export function checkDevice() {
  if (this.utils.isMobile()) {
    console.log('当前是移动端');
    // 移动端特殊处理
  } else {
    console.log('当前是PC端');
    // PC端处理
  }
}
```

---

## 路由导航

### this.utils.router.push(path, params, blank, isUrl, type)
**功能**: 页面跳转  
**参数**:
- `path`: 跳转路径
- `params`: URL参数（可选）
- `blank`: 是否新窗口打开（可选）
- `isUrl`: 是否为完整URL（可选）
- `type`: 跳转类型 'push'|'replace'（可选）
**返回**: void

```javascript
// 基本跳转
export function goToPage() {
  this.utils.router.push('/workbench');
}

// 带参数跳转
export function goWithParams() {
  this.utils.router.push('/user/detail', {
    id: '123',
    tab: 'info'
  });
}

// 新窗口打开
export function openInNewTab() {
  this.utils.router.push('https://www.aliwork.com', null, true, true);
}
```

### this.utils.router.getQuery(key, queryStr)
**功能**: 获取URL参数  
**参数**:
- `key`: 参数名（可选）
- `queryStr`: 自定义查询字符串（可选）
**返回**: 参数值或参数对象

```javascript
export function getUrlParams() {
  // 获取所有参数
  const allParams = this.utils.router.getQuery();
  
  // 获取特定参数
  const userId = this.utils.router.getQuery('userId');
  const tab = this.utils.router.getQuery('tab');
  
  console.log({ allParams, userId, tab });
}
```

---

## 函数调用

### this.methodName(params)
**功能**: 调用动作面板中的其他函数  
**参数**: 函数参数（可选）
**返回**: 函数返回值

```javascript
// 定义一个工具函数
export function showMessage(message, type = 'info') {
  this.utils.toast({
    title: message,
    type: type
  });
}

// 在其他函数中调用
export function handleSuccess() {
  this.showMessage('操作成功！', 'success');
}

export function handleError() {
  this.showMessage('操作失败！', 'error');
}
```

---

## ⚠️ 重要注意事项

### 1. this指向问题
```javascript
// ✅ 正确：在最外层函数中this指向正确
export function correctUsage() {
  const data = this.state.data;
  this.setState({ loading: true });
}

// ❌ 错误：嵌套函数中this指向改变
export function wrongUsage() {
  this.dataSourceMap.api.load(function(res) {
    this.setState({ data: res }); // this指向已改变
  });
}

// ✅ 解决方案1：保存this引用
export function solution1() {
  const that = this;
  this.dataSourceMap.api.load(function(res) {
    that.setState({ data: res });
  });
}

// ✅ 解决方案2：使用箭头函数
export function solution2() {
  this.dataSourceMap.api.load((res) => {
    this.setState({ data: res }); // 箭头函数保持this指向
  });
}
```

### 2. 禁止的用法
```javascript
// ❌ 禁止直接修改state
this.state.data = newData;

// ❌ 禁止直接访问组件属性
const content = this.$('text_abc').content;

// ❌ 禁止直接设置组件属性
this.$('text_abc').content = 'new content';
```

### 3. 推荐的错误处理
```javascript
export function safeApiCall() {
  this.dataSourceMap.api.load()
    .then(response => {
      this.setState({ data: response.data });
    })
    .catch(error => {
      console.error('API调用失败:', error);
      this.utils.toast({
        type: 'error',
        title: '数据加载失败，请重试'
      });
    });
}
```

---

**📝 说明**: 本参考手册涵盖了宜搭平台的所有核心API，在实际开发中请严格按照示例用法使用，避免使用禁止的语法以确保代码的兼容性和稳定性。
