---
title: RateField 评分
order: 13
---

# RateField 评分

## 何时使用

- 通常用于用户反馈场景；

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/rate-field-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'value',
      type: 'number',
      default: '2.5',
      desc: '表单组件默认值',
    },
    {
      code: 'count',
      type: 'number',
      default: '5',
      desc: '评分总数',
    },
    {
      code: 'allowHalf',
      type: 'boolean',
      default: 'false',
      desc: '支持半星评分',
    },
    {
      code: 'showGrade',
      type: 'boolean',
      default: 'false',
      desc: '显示分数',
    },
    {
      code: 'onChange',
      type: '({ value: number }) => void',
      default: '-',
      desc: '用户点击评分时',
    },
    {
      code: 'onHoverChange',
      type: '(value: number) => void',
      default: '-',
      desc: '用户hover评分时',
    },
  ]}
/>