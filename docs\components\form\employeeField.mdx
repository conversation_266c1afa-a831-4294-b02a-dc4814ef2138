---
title: EmployeeField 人员搜索框
order: 12
---

# EmployeeField 人员搜索框

## 何时使用
- 当我们需要在页面中指定处理人的时候；
- 当用户需要登记个人信息的时候；

## 组件示例
import Iframe from 'components/Iframe'

<Iframe url="https://www.aliwork.com/developer/employee-field-v2?isRenderNav=false" />

## 组件属性
import AttrTable from 'components/AttrTable';

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'closeOnSelect',
      type: 'boolean',
      default: 'false',
      desc: '是否在选中后关闭',
    },
    {
      code: 'beforeSearchTeam',
      type: '(params: Record<string, string>) => Record<string, string>',
      default: '-',
      desc: '搜索组织，发送请求前，可以通过此函数处理参数，当**dataType**属性为 url时生效',
    },
    {
      code: 'subUrl',
      type: 'string',
      default: '-',
      desc: '下属异步接口，自定义搜索全员和下属时的服务 URL，默认用 接口地址',
    },
    {
      code: 'fit',
      type: '(response: any) => any',
      default: '-',
      desc: '异步数据结果处理，当**dataType**属性为 url时生效',
    },
    {
      code: 'beforeSearch',
      type: '(params: Record<string, string>) => Record<string, string>',
      default: '-',
      desc: '搜索人员，发送请求前，可以通过此函数处理参数，当**dataType**属性为 url时生效',
    },
    {
      code: 'url',
      type: 'string',
      default: '-',
      desc: '人员信息异步接口地址，当**dataType**属性为 url时生效',
    },
    {
      code: 'dataType',
      type: `'url' | 'dataSource'`,
      default: `'url'`,
      desc: '数据类型，url：接口请求、DataSource：数据源',
    },
    {
      code: 'hasClear',
      type: 'boolean',
      default: 'true',
      desc: '显示清除按钮',
    },
    {
      code: 'placeholder',
      type: 'string',
      default: `'请选择'`,
      desc: '人员搜索框占位提示',
    },
    {
      code: 'value',
      type: 'any[]',
      default: '-',
      desc: '当前组件默认值',
    },
    {
      code: 'emplIdInLabel',
      type: 'boolean',
      default: 'true',
      desc: '是否将工号显示在选中结果中',
    },
    {
      code: 'fetchDataOnMount',
      type: 'boolean',
      default: 'true',
      desc: '是否在页面加载后自动请求接口',
    },
    {
      code: 'hiddenSelected',
      type: 'boolean',
      default: '',
      desc: '',
    },
    {
      code: 'multiple',
      type: 'boolean',
      default: 'false',
      desc: '开启多选模式',
    },
    {
      code: 'orderNum',
      type: 'string',
      default: '',
      desc: '"为 0 时为选人模式（仅展示主岗信息），非 0 为选岗模式（透出多个兼岗信息）"',
    },
    {
      code: 'renderOption',
      type: '(option: any) => string',
      default: '-',
      desc: '自定义选项渲染字段',
    },
    {
      code: 'renderSelection',
      type: '(option: any) => string',
      default: '-',
      desc: '自定义选中后的渲染字段',
    },
    {
      code: 'searchDelay',
      type: 'number',
      default: '100',
      desc: '搜索延时，单位ms',
    },
    {
      code: 'showSub',
      type: 'boolean',
      default: 'false',
      desc: '显示下属',
    },
    {
      code: 'showAvater',
      type: 'boolean',
      default: 'true',
      desc: '是否在选择浮窗中显示头像',
    },
    {
      code: 'showDeptDesc',
      type: 'boolean',
      default: 'true',
      desc: '是否显示部门描述',
    },
    {
      code: 'showEmplId',
      type: 'boolean',
      default: 'false',
      desc: '人员搜索框显示工号',
    },
    {
      code: 'showJobDesc',
      type: 'boolean',
      default: 'true',
      desc: '是否显示职位描述',
    },
    {
      code: 'hasOrderNum',
      type: 'boolean',
      default: '',
      desc: '显示主兼职',
    },
    {
      code: 'showAllSub',
      type: 'boolean',
      default: '',
      desc: '显示全员',
    },
    {
      code: 'dataSource',
      type: 'any[]',
      default: '-',
      desc: '人员数据，当**dataType** 为 dataSource 时生效',
    },
  ]}
/>
