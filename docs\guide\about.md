# 介绍
钉钉宜搭是云钉的低代码开发平台，提供基础的表单、流程、报表等场景搭建能力满足业务需求，还提供更加灵活的自定义页面能力，用于实现更加复杂的业务需求。

本开发者中心主要面向有一定开发经验的用户，帮助他们解锁更多宜搭高级使用技能，利用宜搭建设更加复杂的业务系统。

当然本文档中介绍的部分内容也可以在表单搭建设计器中使用。

钉钉宜搭自定义页面提供了一套前端开放的双端适配的开发环境，和小程序开发一样，提供了丰富应用生命周期的前端 API，也可以直接调用钉钉的 JS API，生产的应用最后可以发布为一个用户可访问的地址（可支持组织内和组织外的访问）。当前单独的钉钉小程序开发还不支持双端，在企业管理场景下，HTML5 的页面更佳适合低成本构建双端适配的场景。宜搭后端的开放，目前体现在可以通过连接器的 Groovy 以及 FaaS 节点来做后端部分定制。

钉钉宜搭自定义页面还可以借助钉钉宜搭应用的表单做数据存储，通过宜搭的 Open API 去做表单的 CRUD（增删改查），把宜搭当做数据库 + 图床使用，用于构建自己的前台展示类产品，如[宜搭行业案例](https://www.aliwork.com/o/customer_cases)页面。这套引擎的效率和稳定性已经经过阿里巴巴很大一部分业务的验证，集团核心的人、财、法、事、物、场等系统都是 base 在这套引擎上。

## 面向用户
* 如果你有一定的程序开发基础且了解 React/Vue 的相关知识，宜搭开发者中心是你提升低代码研发技能的最佳宝典；
* 如果你有一定的程序开发基础但对前端技术方向不太了解，宜搭开发者中心可以帮你快速上手常见场景的低代码搭建技能；
* 如果你是不懂代码，希望通过宜搭快速实现简单的表单、流程或者报表页面，更建议访问 [宜搭产品使用手册](https://docs.aliwork.com/docs/yida_support/ytzzua) 快速了解宜搭基础产品能力后，可以通过开发者中心的案例，照猫画虎，逐渐满足一些个性化需求，成长。

## 使用场景
宜搭开发者中心主要有以下几种使用场景：
* 当你希望从宜搭普通用户晋级到高级用户时，开发者中心提供完善的概念介绍、示例教程和使用按钮帮助你快速解锁高阶宜搭使用技能；
* 在开发新功能时，开发者中心能够帮助你快速找到功能的具体使用方式或 Demo 示例；
* 在开发遇到问题时，开发者中心能够帮助你快速找到类似的 FAQ 或者问题咨询途径；

## 特性 🎉
宜搭自定义页面主要具备以下特性：
* 🦧 提供所见即所得的拖拽式开发模式，官方实现大量常用组件及系统 API，让您轻松上手低代码开发；
* 🦊 提供全栈式解决方案，打通后端数据模型及存储，屏蔽前端工程化细节，让您快速搭建精美页面；
* 🐯 提供灵活的低代码研发模式，通过可视化搭建快速实现 UI 效果，通过 JS 代码编写轻松搞定业务逻辑；

## 常见问题

### 自定义页面和小程序开发的区别是啥?
钉钉宜搭自定义页面提供了一套前端开放的双端适配的开发环境，和小程序开发一样，提供了丰富应用生命周期的前端 API，也可以直接调用钉钉的 JS API，生产的应用最后可以发布为一个用户可访问的地址（可支持组织内和组织外的访问）。当前单独的钉钉小程序开发还不支持双端，在企业管理场景下，HTML5 的页面更佳适合低成本构建双端适配的场景。宜搭后端的开放，目前体现在可以通过连接器的 Groovy 以及 FaaS 节点来做后端部分定制。

未来宜搭会提供单独的低代码小程序开发方案（当前在阿里巴巴内部已经在内测），更灵活、低成本解决企业数字化转型下的产研效率问题。

钉钉宜搭自定义页面还可以借助钉钉宜搭应用的表单做数据存储，通过宜搭的 Open API 去做表单的 CRUD（增删改查），把宜搭当做数据库 + 图床使用，用于构建自己的前台展示类产品，如[宜搭行业案例](https://www.aliwork.com/o/customer_cases)页面。这套引擎的效率和稳定性已经经过阿里巴巴很大一部分业务的验证，集团核心的人、财、法、事、物、场等系统都是 base 在这套引擎上。

### 自定义页面和表单页面的区别？

从钉钉宜搭的实现而言，本质是一套 [基于宜搭自研的 Low-Code Engine](https://github.com/alibaba/lowcode-engine)，同一套标准，但是是不同的物料封装。

自定义页面在物料库及使用场景上和普通表单页有所不同，普通表单页主要用于收集数据或者发布流程，因此可以使用的组件主要以输入类组件为主，另外宜搭对表单提交、预览等表单相关逻辑做了封装，用户使用门槛低，自定义页面支持的组件类型更加丰富，需要通过 JS 代码实现业务逻辑，对用户有一定门槛，但是适用范围更广。

### 自定义页面可以对接第三方服务吗？
可以的，自定义页面提供了远程 API 配置能力，用户不仅可以使用宜搭官方提供的远程 API 还可以使用第三方远程服务，不过使用第三方远程服务对接口有一定要求。

### 自定义页面可以实现免登吗？
可以的，自定义页面和普通的表单页面都为页面类型，因此可以在宜搭的页面设置中设置免登配置。

## 联系我们
* 我们也维护了一个 [开发者论坛](https://developer.aliyun.com/group/yida)，欢迎对低代码领域有兴趣的同学多多交流；
* 关于开发者中心的任何问题，包括示例缺失、有误及体验问题，欢迎直接通过左下角「编辑此页」，我们非常欢迎你的贡献。如果你不方便，也可以通过 [官方渠道](https://www.aliwork.com/o/dev_feedback) 给我们反馈;
