---
title: TextField 输入框
order: 10
---

# TextField 输入框

## 何时使用

- 用于文字类信息输入场景；

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/text-field-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'value',
      type: 'string',
      default: '-',
      desc: '设置输入框默认值',
    },
    {
      code: 'placeholder',
      type: 'string',
      default: `'请输入'`,
      desc: '占位提示',
    },
    {
      code: 'htmlType',
      type: `'input' | 'textarea' | 'password'`,
      default: 'input',
      desc: '设置输入框的类型，input：单行文案、 textarea：多行文案、 password：密码',
    },
    {
      code: 'validationType',
      type: ` 'text' | 'mobile' | 'email' | 'url' | 'chineseID' | 'password'`,
      default: "'text'",
      desc: '输入框的格式',
    },
    {
      code: 'state',
      type: `'error' | 'loading' | 'success' | '' `,
      default: `''`,
      desc: '设置输入框state状态',
    },
    {
      code: 'rows',
      type: 'number',
      default: `4`,
      desc: '多行输入框高度，当 **htmlType** 属性为 textarea 时生效',
    },
    {
      code: 'hasLimitHint',
      type: 'boolean',
      default: 'false',
      desc: '是否显示计数器',
    },
    {
      code: 'maxLength',
      type: 'number',
      default: '200',
      desc: '设置字数上限',
    },
    {
      code: 'autoHeight',
      type: 'boolean',
      default: 'false',
      desc: '多行输入框自动高度，当 **htmlType** 属性为 textarea 时生效',
    },
    {
      code: 'hasClear',
      type: 'boolean',
      default: 'true',
      desc: '是否显示清除按钮',
    },
    {
      code: 'trim',
      type: 'boolean',
      default: 'false',
      desc: '是否自动去除头尾空字符',
    },
    {
      code: 'scanCode',
      type: '[ScanCodeConfig](/docs/components/interface#scancodeconfig) | boolean',
      default: 'false',
      desc: '是否启用扫码模式（仅钉钉手机端内支持）',
    },
    {
      code: 'autoFocus',
      type: 'boolean',
      default: 'false',
      desc: '是否开启自动聚焦',
    },
    {
      code: 'addonBefore',
      type: 'string',
      default: `-`,
      desc: '输入框前附加内容',
    },
    {
      code: 'addonAfter',
      type: 'string',
      default: `-`,
      desc: '输入框后附加内容',
    },
    {
      code: 'onChange',
      type: '({ value: string }) => void',
      default: '',
      desc: '输入框组件值发生改变时触发的事件',
    },
    {
      code: 'onPressEnter',
      type: '() => void',
      default: '',
      desc: '输入框按下回车键时触发的事件',
    },
    {
      code: 'onScanCodeSuccess',
      type: '(text: string) => void',
      default: '',
      desc: '输入框扫码成功后触发的事件',
    },
    {
      code: 'onScanCodeError',
      type: '(errorMsg: string) => void',
      default: '',
      desc: '输入框扫码失败后触发的事件',
    },
  ]}
/>
