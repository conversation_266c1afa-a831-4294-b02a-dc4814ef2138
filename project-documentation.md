# 钉钉宜搭开发者中心项目文档

## 项目概述

**项目名称**: 钉钉宜搭开发者中心  
**项目描述**: 宜搭开发者中心是通过 Docusaurus 2 构建的一个开发者平台，用于沉淀宜搭面向开发者的使用案例和教程。  
**版本**: 0.1.0  
**技术栈**: Docusaurus 2, React, TypeScript, Sass  

### 在线地址
- **GitHub Pages**: [https://dingtalk-yida.github.io/developer-site/](https://dingtalk-yida.github.io/developer-site/)
- **国内镜像**: [https://yida-developer.gitee.io/](https://yida-developer.gitee.io/)
- **仓库地址**: [https://github.com/dingtalk-yida/developer-site](https://github.com/dingtalk-yida/developer-site)

## 项目结构

```
developer-site/
├── README.md                    # 项目说明文档
├── package.json                 # 项目依赖配置
├── docusaurus.config.js         # Docusaurus 主配置文件
├── docusaurus.gitee.config.js   # Gitee 部署配置
├── babel.config.js              # Babel 配置
├── tsconfig.json               # TypeScript 配置
├── config/                     # 配置文件目录
│   ├── customFields.js         # 自定义字段配置
│   ├── footer.js               # 页脚配置
│   ├── navbar.js               # 导航栏配置
│   ├── plugin.js               # 插件配置
│   └── sidebars.js             # 侧边栏配置
├── docs/                       # 文档内容目录
│   ├── api/                    # API 文档
│   ├── components/             # 组件文档
│   ├── guide/                  # 指南文档
│   ├── tutorial/               # 教程文档
│   └── usage/                  # 使用说明文档
├── src/                        # 源代码目录
│   ├── components/             # React 组件
│   ├── css/                    # 样式文件
│   ├── pages/                  # 页面文件
│   ├── theme/                  # 主题定制
│   └── utils/                  # 工具函数
├── static/                     # 静态资源
│   ├── file/                   # 文件资源
│   ├── img/                    # 图片资源
│   └── init.js                 # 初始化脚本
└── scripts/                    # 构建脚本
    └── getDocsFromDir.js       # 文档目录获取脚本
```

## 核心功能

### 1. 宜搭平台介绍
钉钉宜搭是云钉的低代码开发平台，提供：
- 基础的表单、流程、报表等场景搭建能力
- 灵活的自定义页面能力
- 双端适配的开发环境
- 丰富的应用生命周期前端 API
- 直接调用钉钉 JS API 的能力

### 2. 面向用户群体
- **有开发基础且了解 React/Vue**: 提升低代码研发技能的最佳宝典
- **有开发基础但前端技术不熟**: 快速上手常见场景的低代码搭建技能
- **不懂代码的用户**: 通过案例照猫画虎，逐渐满足个性化需求

### 3. 主要特性
- 🦧 提供所见即所得的拖拽式开发模式
- 🦊 提供全栈式解决方案，打通后端数据模型及存储
- 🐯 提供灵活的低代码研发模式

## 技术架构

### 核心依赖
```json
{
  "@docusaurus/core": "^2.0.0-beta.17",
  "@docusaurus/preset-classic": "^2.0.0-beta.17",
  "react": "^17.0.1",
  "react-dom": "^17.0.1",
  "antd": "^4.19.2",
  "@ant-design/icons": "^4.8.0"
}
```

### 开发依赖
- **搜索功能**: @easyops-cn/docusaurus-search-local
- **中文分词**: nodejieba
- **样式处理**: sass, docusaurus-plugin-sass
- **代码质量**: eslint, stylelint, husky, lint-staged
- **部署工具**: gh-pages

### 插件配置
- **图片缩放**: plugin-image-zoom
- **本地搜索**: @easyops-cn/docusaurus-search-local
- **热力图分析**: docusaurus-plugin-hotjar
- **Sass 支持**: docusaurus-plugin-sass

## 开发指南

### 环境要求
- Node.js >= 14
- npm 或 yarn

### 安装依赖
```bash
npm install
```

**Windows 用户注意**: 如果安装失败，需要先安装 node-gyp：
```bash
npm install node-gyp nodejieba@2.5.2
```

### 本地开发
```bash
npm start
```
启动后访问: [http://localhost:3000/developer-site/](http://localhost:3000/developer-site)

> 注意：本地启动情况下，全局搜索功能不生效

### 构建项目
```bash
npm run build
```
构建产物将输出到 `build` 目录

### 部署流程

#### GitHub Pages 部署
```bash
npm run deploy
```
自动构建并推送到 GitHub 的 `site` 分支

#### Gitee 镜像部署
1. 添加 Gitee 远程仓库：
```bash
git remote add gitee https://gitee.com/yida-developer/yida-developer.git
```

2. 部署到 Gitee：
```bash
npm run deploy:gitee
```

3. 前往 [Gitee Pages 管理页](https://gitee.com/yida-developer/yida-developer/pages) 执行更新操作

## 文档结构

### 指南文档 (docs/guide/)
- **about.md**: 平台介绍和概述
- **start.md**: 快速开始教程
- **keywords.md**: 关键词说明
- **designer.md**: 设计器使用说明
- **concept/**: 核心概念文档
- **customComponent/**: 自定义组件文档
- **FAQ/**: 常见问题解答
- **contributing.md**: 贡献指南

### API 文档 (docs/api/)
- **yidaAPI.md**: 宜搭 JS-API 文档
- **openAPI.md**: 开放 API 文档
- **serverAPI.md**: 服务端 API 文档
- **dingAPI.md**: 钉钉 API 文档

### 组件文档 (docs/components/)
- **layout/**: 布局组件 (容器、标签页布局等)
- **basic/**: 基础组件 (按钮、文本、图片等)
- **form/**: 表单组件 (输入框、选择器、日期等)
- **advanced/**: 高级组件 (表格、树形、进度条等)

### 教程文档 (docs/tutorial/)
- **todoMVC.md**: TodoMVC 完整教程

### 使用说明 (docs/usage/)
- **quickStart.md**: 快速开始
- **guide/**: 使用指南
- **intro/**: 产品介绍
- **changeLog.md**: 更新日志
- **price.md**: 价格说明
- **dataSecurity.md**: 数据安全

## 配置说明

### Docusaurus 配置 (docusaurus.config.js)
- **站点信息**: 标题、描述、URL 配置
- **国际化**: 支持中文 (zh-Hans)
- **主题配置**: 导航栏、页脚、代码高亮
- **SEO 优化**: 元数据、Open Graph 配置
- **插件集成**: 搜索、图片缩放、Sass 支持

### 侧边栏配置 (config/sidebars.js)
自动从目录结构生成侧边栏导航，支持分类和层级结构

### 导航栏配置 (config/navbar.js)
顶部导航菜单配置，包含主要文档分类入口

## 贡献指南

### 参与方式
1. **问题反馈**: 通过页面左下角「编辑此页」提交改进建议
2. **官方渠道**: [官方反馈页面](https://www.aliwork.com/o/dev_feedback)
3. **开发者论坛**: [阿里云开发者社区](https://developer.aliyun.com/group/yida)

### 内容贡献
- 示例缺失或错误修正
- 文档体验优化
- 新功能使用案例
- FAQ 补充

## 联系方式

- **开发者论坛**: [https://developer.aliyun.com/group/yida](https://developer.aliyun.com/group/yida)
- **官方反馈**: [https://www.aliwork.com/o/dev_feedback](https://www.aliwork.com/o/dev_feedback)
- **在线示例**: [https://www.aliwork.com/o/demo/hello](https://www.aliwork.com/o/demo/hello)
- **设计器体验**: [https://www.aliwork.com/developer/designer](https://www.aliwork.com/developer/designer)

## 快速开始示例

### Hello World 示例
这是一个简单的问候语生成器，展示了宜搭自定义页面的基本开发流程：

#### 功能描述
- 用户在输入框中输入姓名
- 点击生成按钮
- 自动生成问候语并显示

#### 实现步骤

**1. 创建宜搭应用**
在宜搭工作台中创建一个空白应用

**2. 创建自定义页面**
选择自定义页面模板，进入设计器

**3. 添加组件**
- 输入框组件：设置标题为"姓名"
- 按钮组件：设置标题为"生成"
- 文本组件：用于显示问候语，设置样式（上边距20px，字体大小20px）

**4. 创建全局变量**
创建名为 `helloWord` 的全局变量用于存储问候语

**5. 绑定按钮点击事件**
```javascript
export function onClick() {
  // 获取姓名输入框内容
  const name = this.$('textField_kzdxqcod').getValue();

  // 更新全局变量
  this.setState({
    helloWord: `Hello ${name} !`,
  });
}
```

**6. 绑定数据源**
将全局变量 `helloWord` 绑定到文本组件的内容属性

**7. 预览和发布**
点击预览按钮测试功能，保存后发布应用

### 在线体验
- **示例地址**: [https://www.aliwork.com/o/demo/hello](https://www.aliwork.com/o/demo/hello)
- **设计器查看**: [https://www.aliwork.com/developer/designer?formUuid=hello-v2](https://www.aliwork.com/developer/designer?formUuid=hello-v2)

## 核心 API 说明

### 宜搭 JS-API 概览

宜搭平台提供了丰富的 JavaScript API，可以在 JS 面板或变量绑定弹框中直接调用：

#### 基础 API
- **this.state**: 获取全局变量状态
- **this.setState()**: 设置全局变量状态
- **this.$()**: 获取组件实例
- **this.dataSourceMap**: 数据源映射

#### 组件操作 API
- **getValue()**: 获取组件值
- **setValue()**: 设置组件值
- **setProps()**: 设置组件属性
- **show()/hide()**: 显示/隐藏组件

#### 数据源 API
- **load()**: 加载数据源
- **reload()**: 重新加载数据源
- **submit()**: 提交数据

#### 页面导航 API
- **this.utils.router**: 路由工具
- **this.utils.toast**: 消息提示
- **this.utils.dialog**: 对话框

### 注意事项

#### this 指向问题
```javascript
// 正确用法
export function setSomeValue() {
  const status = this.state.status;
  this.setState({ status: status + 1 });
}

// 错误用法 - 嵌套函数中 this 指向改变
export function setSomeValue() {
  this.dataSourceMap.xxx.load(function (ret) {
    // 错误！this 指向已改变
    this.$('numberField_xxx').setValue(ret);
  });
}

// 解决方案1 - 保存 this 引用
export function setSomeValue() {
  const that = this;
  this.dataSourceMap.xxx.load(function (ret) {
    that.$('numberField_xxx').setValue(ret);
  });
}

// 解决方案2 - 使用箭头函数
export function setSomeValue() {
  this.dataSourceMap.xxx.load((ret) => {
    this.$('numberField_xxx').setValue(ret);
  });
}
```

## 组件库说明

### 布局组件
- **容器 (Container)**: 基础布局容器
- **区域容器 (RegionalContainer)**: 带标题的区域容器
- **标签页布局 (TabsLayout)**: 多标签页布局

### 基础组件
- **按钮 (Button)**: 各种类型的按钮
- **文本 (Text)**: 文本显示组件
- **图片 (Image)**: 图片展示组件
- **链接 (Link)**: 超链接组件
- **图标 (Icon)**: 图标组件
- **视频 (Video)**: 视频播放组件

### 表单组件
- **输入框 (TextField)**: 单行文本输入
- **多行输入框 (TextareaField)**: 多行文本输入
- **数字输入框 (NumberField)**: 数字输入
- **选择器 (SelectField)**: 下拉选择
- **多选器 (MultiSelectField)**: 多选下拉
- **单选框 (RadioField)**: 单选按钮组
- **复选框 (CheckboxField)**: 复选框组
- **日期选择器 (DateField)**: 日期时间选择
- **级联选择器 (CascadeSelectField)**: 级联下拉选择
- **员工选择器 (EmployeeField)**: 组织架构人员选择
- **附件上传 (AttachmentField)**: 文件上传
- **图片上传 (ImageField)**: 图片上传
- **富文本编辑器 (EditorField)**: 富文本编辑
- **评分组件 (RateField)**: 星级评分
- **表格组件 (TableField)**: 数据表格

### 高级组件
- **表格 (Table)**: 数据展示表格
- **树形控件 (Tree)**: 树形数据展示
- **分页器 (Pagination)**: 分页导航
- **进度条 (Progress)**: 进度显示
- **步骤条 (Steps)**: 步骤导航
- **时间轴 (TimeLine)**: 时间线展示
- **菜单 (Menu)**: 导航菜单
- **搜索框 (Search)**: 搜索输入
- **滑块 (Slider)**: 数值滑块
- **筛选器 (Filter)**: 数据筛选
- **气泡卡片 (Balloon)**: 气泡提示
- **HTML (HTML)**: 自定义 HTML
- **内嵌页面 (Iframe)**: 内嵌外部页面
- **JSX**: 自定义 JSX 组件

## 常见问题 FAQ

### 开发相关

**Q: 自定义页面和小程序开发的区别是什么？**
A: 宜搭自定义页面提供双端适配的开发环境，支持 HTML5 页面，更适合企业管理场景。小程序开发当前不支持双端，但宜搭未来会提供单独的低代码小程序开发方案。

**Q: 自定义页面和表单页面的区别？**
A: 两者基于同一套 Low-Code Engine，但物料封装不同。表单页主要用于数据收集和流程发布，组件以输入类为主；自定义页面组件类型更丰富，需要通过 JS 代码实现业务逻辑。

**Q: 自定义页面可以对接第三方服务吗？**
A: 可以。自定义页面提供远程 API 配置能力，支持使用第三方远程服务，但对接口有一定要求。

**Q: 自定义页面可以实现免登吗？**
A: 可以。在宜搭的页面设置中可以配置免登功能。

### 技术问题

**Q: Windows 下 npm 安装失败怎么办？**
A: 这是因为 nodejieba 依赖问题。解决方案：
```bash
npm install node-gyp nodejieba@2.5.2
```

**Q: 本地启动后搜索功能不生效？**
A: 这是正常现象。本地开发环境下全局搜索功能不生效，需要在构建后的生产环境中才能正常使用。

**Q: 如何自定义主题样式？**
A: 可以通过修改 `src/css/custom.css` 文件来自定义样式，或者在 `src/theme/` 目录下进行主题组件的定制。

---

*本文档基于项目当前状态自动生成，如有更新请及时同步修改。*
