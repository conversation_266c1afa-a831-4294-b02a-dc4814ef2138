---
title: Text 文本
order: 3
---

# Text 文本

文本展示。

## 何时使用

- 通过文本展示描述信息。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/text-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'content',
      type: 'string',
      default: `'文本'`,
      desc: '文案内容',
    },
    {
      code: 'showTitle',
      type: 'boolean',
      default: 'false',
      desc: '是否将内容显示为 html 标签的 title属性上',
    },
    {
      code: 'behavior',
      type: `'NORMAL' | 'HIDDEN'`,
      default: `'NORMAL'`,
      desc: '默认状态',
    },
    {
      code: 'maxLine',
      type: 'number',
      default: '0',
      desc: '最大行数,超出时自动省略号显示',
    },
    {
      code: 'contentPaddingMobile',
      type: `'0 | 16'`,
      default: `'0'`,
      desc: '手机端左右间距，单位为px',
    },
  ]}
/>

